{"name": "stresser-service", "version": "1.0.0", "description": "Multilingual stresser/booter service with Node.js, MongoDB, Binance payment, and admin panel.", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js"}, "dependencies": {"axios": "^1.6.7", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "i18n": "^0.15.1", "mongoose": "^7.6.1", "stresser-service": "file:"}, "devDependencies": {"nodemon": "^3.0.1"}}