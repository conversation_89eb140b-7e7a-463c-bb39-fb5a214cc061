{"name": "next-i18next", "version": "14.0.3", "repository": "**************:i18next/next-i18next.git", "author": "i18next", "funding": [{"type": "individual", "url": "https://locize.com/i18next.html"}, {"type": "individual", "url": "https://www.i18next.com/how-to/faq#i18next-is-awesome.-how-can-i-support-the-project"}, {"type": "individual", "url": "https://locize.com"}], "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "types": "dist/types/types.d.ts", "license": "MIT", "engines": {"node": ">=14"}, "description": "The easiest way to translate your NextJs apps.", "keywords": ["react", "i18next", "nextjs", "next", "translation", "localisation", "localization", "locale"], "scripts": {"prettier": "prettier --ignore-path .gitignore --write .", "lint": "eslint src examples", "clean": "rm -rf ./examples/simple/.next ./examples/ssg/.next ./dist && mkdir dist", "build:es": "BABEL_ENV=es babel src --extensions '.ts,.tsx' --out-dir dist/es --copy-files", "build:cjs": "BABEL_ENV=cjs babel src --extensions '.ts,.tsx' --out-dir dist/commonjs --copy-files", "build:esm": "BABEL_ENV=esm babel src --extensions '.ts,.tsx' --out-dir dist/esm --copy-files", "build:types": "tsc --noEmit false --declaration --emitDeclarationOnly --outDir dist/types", "build": "npm-run-all -s clean build:cjs build:es build:esm build:types", "build:examples": "npm-run-all -s build:example:*", "build:example:simple": "cd examples/simple && npm run build", "build:example:ssg": "cd examples/ssg && npm run build", "typecheck": "tsc --project ./tsconfig.json --noEmit", "typecheck:example:simple": "cd examples/simple && npm run typecheck", "check-size": "size-limit", "check-size:why": "size-limit --why", "check-dist": "npm-run-all -s check-dist:*", "check-dist:browser-cjs": "es-check --not 'dist/**/*.map.js,dist/commonjs/createClient/package.json,dist/commonjs/**node**.js,dist/commonjs/serverSideTranslations.js' -v es2017 './dist/commonjs/**/*'", "check-dist:browser-esm": "es-check --not 'dist/**/*.map.js,dist/esm/createClient/package.json,dist/**node**.js,dist/esm/serverSideTranslations.js' -v es2017 --module './dist/esm/**/*'", "test": "NODE_ENV=test jest --maxWorkers=1 --silent", "install:examples": "npm-run-all -s install:example:*", "install:example:simple": "cd examples/simple && npm install", "install:example:ssg": "cd examples/ssg && npm install", "move-build-to-examples?": "Hack: see # @link https://github.com/i18next/next-i18next/pull/2012", "move-build-to-examples": "rimraf ./examples/*/node_modules/next-i18next && cpy './package.json' './*.js' './*.ts' './dist' ./examples/simple/node_modules/next-i18next && cpy './package.json' './*.js' './*.ts' './dist' ./examples/ssg/node_modules/next-i18next", "test:e2e": "start-server-and-test 'cd examples/simple && npm run start' http://127.0.0.1:3000 cy:run", "cy:run": "cypress run", "example": "npm run example:simple", "example:prod": "npm run example:simple:prod", "example:simple": "npm run build && cp -r dist examples/simple/node_modules/next-i18next && cd examples/simple && npm run dev", "example:simple:prod": "npm run build:example:simple && cd examples/simple && npm run start", "example:ssg": "npm run build && cp -r dist examples/ssg/node_modules/next-i18next && cd examples/ssg && npm run dev", "example:ssg:prod": "npm run build:example:ssg && cd examples/ssg && npm run start", "prepublishOnly": "npm run build", "contributors:check": "all-contributors check", "contributors:add": "all-contributors add", "contributors:generate": "all-contributors generate", "preversion": "npm run test && npm run build && git push", "postversion": "git push && git push --tags && npm run release", "release": "gh-release", "nuke:install": "rimraf '**/node_modules' '**/package-lock.json'", "prepare": "husky install"}, "devDependencies": {"@babel/cli": "7.20.7", "@babel/core": "7.20.12", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-transform-runtime": "7.19.6", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "@babel/preset-typescript": "7.18.6", "@size-limit/file": "^8.1.2", "@size-limit/webpack": "^8.1.2", "@size-limit/webpack-why": "^8.1.2", "@testing-library/react": "^13.4.0", "@types/jest": "^29.4.0", "@types/node": "^18.13.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/testing-library__cypress": "^5.0.9", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "all-contributors-cli": "^6.24.0", "babel-core": "7.0.0-bridge.0", "babel-plugin-add-module-exports": "1.0.4", "babel-plugin-transform-async-to-generator": "6.24.1", "cpy-cli": "^4.2.0", "cypress": "^11.0.1", "es-check": "^7.1.0", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-react": "^7.32.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "gh-release": "7.0.2", "husky": "^8.0.3", "i18next": "^23.4.6", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "next": "^13.1.6", "npm-run-all": "^4.1.5", "prettier": "2.8.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.2.1", "rimraf": "^4.1.2", "size-limit": "^8.1.2", "start-server-and-test": "^1.15.3", "typescript": "^4.9.5", "webpack": "^5.75.0"}, "dependencies": {"@babel/runtime": "^7.20.13", "@types/hoist-non-react-statics": "^3.3.1", "core-js": "^3", "hoist-non-react-statics": "^3.3.2", "i18next-fs-backend": "^2.1.5"}, "peerDependencies": {"i18next": "^23.4.6", "next": ">= 12.0.0", "react": ">= 17.0.2", "react-i18next": "^13.2.1"}}