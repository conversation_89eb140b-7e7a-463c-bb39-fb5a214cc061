<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stresser.ba - The Ultimate Choice for Free IP Stresser</title>
    <meta name="description" content="Stresser.ba, the Ultimate Choice for Free IP Stresser, Booter, and Leading Layer 7 Services.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f8fafc;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Navigation */
        .navbar {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #334155;
            padding: 1rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffffff;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #ffffff;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        /* Hero Section */
        .hero {
            padding: 8rem 0 4rem;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.25rem;
            color: #cbd5e1;
            margin-bottom: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-secondary {
            background: #374151;
            color: white;
            padding: 0.75rem 1.5rem;
            border: 1px solid #4b5563;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
            border-color: #6b7280;
        }
        
        /* Features Section */
        .features {
            padding: 4rem 0;
            background: #1e293b;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 3rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            width: 3rem;
            height: 3rem;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        /* Pricing Section */
        .pricing {
            padding: 4rem 0;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .pricing-card {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            position: relative;
        }
        
        .pricing-card.popular {
            border-color: #3b82f6;
            transform: scale(1.05);
        }
        
        .pricing-card h3 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .price {
            font-size: 2rem;
            font-weight: bold;
            color: #60a5fa;
            margin-bottom: 1rem;
        }
        
        .features-list {
            list-style: none;
            margin: 2rem 0;
        }
        
        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #475569;
        }
        
        /* Footer */
        .footer {
            background: #0f172a;
            border-top: 1px solid #334155;
            padding: 2rem 0;
            text-align: center;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
        
        /* Status indicator */
        .status {
            position: fixed;
            top: 80px;
            right: 20px;
            background: #059669;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Status Indicator -->
    <div class="status">
        🟢 Backend: Online
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="logo">STRESSER.ba</a>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li><a href="#" onclick="showLogin()">Login</a></li>
                </ul>
                <a href="#" class="btn-primary" onclick="showRegister()">Register</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <h1>
                Stresser.ba, the Ultimate Choice for 
                <span class="gradient-text">Free IP Stresser</span>, 
                Booter, and Leading Layer 7 Services.
            </h1>
            <p>
                Are you searching for an <strong>IP stresser</strong> service? If you ended up here, you just found the best <strong>IP booter</strong> to protect any of your application Layer4, Layer7 etc..
                <br><br>
                With our <strong>Cryptostresser</strong> you have a large selection of personalization to <strong>instant stress</strong> all your servers.
            </p>
            <div class="hero-buttons">
                <a href="#" class="btn-primary" onclick="showRegister()">Sign Up</a>
                <a href="#" class="btn-secondary" onclick="showLogin()">Login</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Features</h2>
            <p style="text-align: center; color: #cbd5e1; font-size: 1.25rem;">Some features about our system</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>Secure Payment</h3>
                    <p>We handle all of your payment by cryptocurrency with our own nodes to ensure 100% anonymity.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>Your safety is important for us</h3>
                    <p>Stress tests are started from multiple places and can not be monitored. Your privacy is safe with us.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>24/7 Support Team</h3>
                    <p>Our support is here to help you, if you need anything you can contact us.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Modern attack methods</h3>
                    <p>Our ddos attack methods are capable of bypassing the latest protections and our engineers are constantly improving them.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🌍</div>
                    <h3>Join us</h3>
                    <p>A reliable Booter and Webstresser, ran by experienced persons in DDoS area. We provide the best DDoS for hire service.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Pricing</h2>
            
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>Free Package</h3>
                    <div class="price">€0</div>
                    <p>Lifetime</p>
                    <ul class="features-list">
                        <li>Max. Stress Time: 180s</li>
                        <li>Concurrents: 1</li>
                        <li>Premium Network: ❌</li>
                        <li>API access: ❌</li>
                    </ul>
                    <a href="#" class="btn-secondary" onclick="showRegister()">Get Started</a>
                </div>
                
                <div class="pricing-card popular">
                    <h3>Premium #1</h3>
                    <div class="price">€180</div>
                    <p>Monthly</p>
                    <ul class="features-list">
                        <li>Max. Stress Time: 1200s</li>
                        <li>Concurrents: 2</li>
                        <li>Premium Network: ✅</li>
                        <li>API access: ✅</li>
                    </ul>
                    <a href="#" class="btn-primary" onclick="showRegister()">Choose Plan</a>
                </div>
                
                <div class="pricing-card">
                    <h3>Premium #5</h3>
                    <div class="price">€900</div>
                    <p>Monthly</p>
                    <ul class="features-list">
                        <li>Max. Stress Time: 7200s</li>
                        <li>Concurrents: 10</li>
                        <li>Premium Network: ✅</li>
                        <li>API access: ✅</li>
                    </ul>
                    <a href="#" class="btn-primary" onclick="showRegister()">Choose Plan</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>© 2025 stresser.ba - All rights Reserved.</p>
            <p style="margin-top: 1rem;">
                <a href="https://t.me/ipstresser" target="_blank" style="color: #60a5fa;">Telegram</a>
            </p>
        </div>
    </footer>

    <script>
        function showLogin() {
            alert('Login functionality will be implemented with the full React application.\n\nFor now, you can test the backend API at:\nhttp://localhost:5000/api/health');
        }
        
        function showRegister() {
            alert('Registration functionality will be implemented with the full React application.\n\nFor now, you can test the backend API at:\nhttp://localhost:5000/api/health');
        }
        
        // Test backend connection
        async function testBackend() {
            try {
                const response = await fetch('http://localhost:5000/api/settings/public');
                if (response.ok) {
                    document.querySelector('.status').innerHTML = '🟢 Backend: Online';
                    document.querySelector('.status').style.background = '#059669';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.querySelector('.status').innerHTML = '🔴 Backend: Offline';
                document.querySelector('.status').style.background = '#dc2626';
            }
        }
        
        // Test backend on page load
        testBackend();
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
